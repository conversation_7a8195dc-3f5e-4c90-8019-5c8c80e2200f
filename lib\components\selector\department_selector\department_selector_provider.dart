import 'package:flutter/material.dart';
import 'package:octasync_client/api/department.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'package:octasync_client/models/pages_model/pages_model.dart';

/// 部门选择器状态管理 Provider
class DepartmentSelectorProvider extends ChangeNotifier {
  // 私有变量区域
  final Map<String, dynamic> _reqParams = {'PageIndex': 1, 'PageSize': 9999};
  PagesModel<DepartmentModel> _pages = PagesModel();
  List<DepartmentModel> _allDepartments = [];
  List<DepartmentModel> _filteredDepartments = [];
  String? _searchQuery;
  bool _isLoading = false;
  String? _error;

  // 选中状态管理
  final Set<String> _checkedDepartmentIds = <String>{};
  String? _selectedDepartmentId; // 高亮选中的部门ID

  // 公开属性区域
  
  /// 获取所有部门数据
  List<DepartmentModel> get allDepartments => _allDepartments;
  
  /// 获取过滤后的部门数据
  List<DepartmentModel> get filteredDepartments => _filteredDepartments;
  
  /// 获取当前搜索查询
  String? get searchQuery => _searchQuery;
  
  /// 获取加载状态
  bool get isLoading => _isLoading;
  
  /// 获取错误信息
  String? get error => _error;
  
  /// 获取分页信息
  PagesModel<DepartmentModel> get pages => _pages;
  
  /// 获取所有被选中的部门ID集合
  Set<String> get checkedDepartmentIds => Set.from(_checkedDepartmentIds);
  
  /// 获取当前高亮选中的部门ID
  String? get selectedDepartmentId => _selectedDepartmentId;
  
  /// 获取所有被选中的部门对象
  List<DepartmentModel> get checkedDepartments {
    return _allDepartments.where((dept) => 
      dept.id != null && _checkedDepartmentIds.contains(dept.id!)
    ).toList();
  }

  // 方法区域
  
  /// 初始化加载部门数据
  Future<void> loadDepartments() async {
    _setLoading(true);
    _setError(null);
    
    try {
      final response = await DepartmentApi.getList(_reqParams);
      _pages = PagesModel.fromJson(
        response,
        (json) => DepartmentModel.fromJson(json as Map<String, dynamic>),
      );
      _allDepartments = _pages.items;
      _applySearchFilter();
      
      _setLoading(false);
    } catch (e) {
      _setError('加载部门数据失败: $e');
      _setLoading(false);
    }
  }
  
  /// 刷新部门数据
  Future<void> refresh() async {
    await loadDepartments();
  }
  
  /// 设置搜索查询并应用过滤
  void setSearchQuery(String? query) {
    if (_searchQuery == query) return;
    
    _searchQuery = query?.trim();
    _applySearchFilter();
    notifyListeners();
  }
  
  /// 应用搜索过滤逻辑
  void _applySearchFilter() {
    if (_searchQuery == null || _searchQuery!.isEmpty) {
      // 无搜索条件时显示所有部门
      _filteredDepartments = List.from(_allDepartments);
    } else {
      // 有搜索条件时进行过滤，保持层级结构
      _filteredDepartments = _filterDepartmentsWithHierarchy(_searchQuery!);
    }
  }
  
  /// 过滤部门并保持层级结构完整
  List<DepartmentModel> _filterDepartmentsWithHierarchy(String query) {
    final Set<String> matchedIds = <String>{};
    final Set<String> requiredParentIds = <String>{};
    
    // 第一步：找到所有匹配的部门
    for (var dept in _allDepartments) {
      if (dept.departmentName.contains(query)) {
        if (dept.id != null) {
          matchedIds.add(dept.id!);
          // 添加所有上级部门ID到必需列表
          _addParentIds(dept, requiredParentIds);
        }
      }
    }
    
    // 第二步：合并匹配的部门和必需的上级部门
    final Set<String> finalIds = <String>{};
    finalIds.addAll(matchedIds);
    finalIds.addAll(requiredParentIds);
    
    // 第三步：返回过滤后的部门列表
    return _allDepartments.where((dept) => 
      dept.id != null && finalIds.contains(dept.id!)
    ).toList();
  }
  
  /// 递归添加部门的所有上级部门ID
  void _addParentIds(DepartmentModel dept, Set<String> parentIds) {
    if (dept.parentIdList.isNotEmpty) {
      for (String parentId in dept.parentIdList) {
        if (parentId.isNotEmpty) {
          parentIds.add(parentId);
        }
      }
    }
  }
  
  /// 设置部门复选框选中状态
  void setDepartmentChecked(String departmentId, bool checked) {
    if (checked) {
      _checkedDepartmentIds.add(departmentId);
    } else {
      _checkedDepartmentIds.remove(departmentId);
    }
    notifyListeners();
  }
  
  /// 批量设置部门选中状态
  void setMultipleDepartmentsChecked(List<String> departmentIds, bool checked) {
    if (checked) {
      _checkedDepartmentIds.addAll(departmentIds);
    } else {
      _checkedDepartmentIds.removeAll(departmentIds);
    }
    notifyListeners();
  }
  
  /// 清除所有选中状态
  void clearAllChecked() {
    _checkedDepartmentIds.clear();
    notifyListeners();
  }
  
  /// 设置高亮选中的部门
  void setSelectedDepartment(String? departmentId) {
    if (_selectedDepartmentId != departmentId) {
      _selectedDepartmentId = departmentId;
      notifyListeners();
    }
  }
  
  /// 检查部门是否被选中（复选框）
  bool isDepartmentChecked(String departmentId) {
    return _checkedDepartmentIds.contains(departmentId);
  }
  
  /// 检查部门是否被高亮选中
  bool isDepartmentSelected(String departmentId) {
    return _selectedDepartmentId == departmentId;
  }
  
  // 私有辅助方法
  
  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }
  
  /// 设置错误信息
  void _setError(String? error) {
    if (_error != error) {
      _error = error;
      notifyListeners();
    }
  }
}
