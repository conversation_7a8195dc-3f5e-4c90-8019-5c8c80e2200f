import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'department_tree.dart';
import 'department_selector_provider.dart';

/// 部门选择器使用示例
/// 展示如何使用搜索功能和 Provider 状态管理
class DepartmentSelectorExample extends StatefulWidget {
  const DepartmentSelectorExample({super.key});

  @override
  State<DepartmentSelectorExample> createState() => _DepartmentSelectorExampleState();
}

class _DepartmentSelectorExampleState extends State<DepartmentSelectorExample> {
  final TextEditingController _searchController = TextEditingController();
  final GlobalKey<DepartmentTreeState> _treeKey = GlobalKey<DepartmentTreeState>();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('部门选择器示例')),
      body: Column(
        children: [
          // 搜索栏
          _buildSearchBar(),

          // 操作按钮栏
          _buildActionBar(),

          // 部门树（使用 Provider 模式）
          Expanded(
            child: ChangeNotifierProvider(
              create: (_) => DepartmentSelectorProvider()..loadDepartments(),
              child: DepartmentTree(
                key: _treeKey,
                showCheckbox: true,
                onNodeTap: _onNodeTap,
                onNodeSelected: _onNodeSelected,
              ),
            ),
          ),

          // 选中结果显示
          _buildSelectedResults(),
        ],
      ),
    );
  }

  /// 构建搜索栏
  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: '搜索部门名称...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon:
              _searchController.text.isNotEmpty
                  ? IconButton(
                    icon: const Icon(Icons.clear),
                    onPressed: () {
                      _searchController.clear();
                      _performSearch('');
                    },
                  )
                  : null,
          border: const OutlineInputBorder(),
        ),
        onChanged: _performSearch,
      ),
    );
  }

  /// 构建操作按钮栏
  Widget _buildActionBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        children: [
          ElevatedButton(onPressed: _refreshData, child: const Text('刷新数据')),
          const SizedBox(width: 8),
          ElevatedButton(onPressed: _clearAllSelections, child: const Text('清除选择')),
          const SizedBox(width: 8),
          ElevatedButton(onPressed: _showSelectedDepartments, child: const Text('查看选中')),
        ],
      ),
    );
  }

  /// 构建选中结果显示
  Widget _buildSelectedResults() {
    return Consumer<DepartmentSelectorProvider>(
      builder: (context, provider, child) {
        final checkedDepartments = provider.checkedDepartments;

        if (checkedDepartments.isEmpty) {
          return Container(padding: const EdgeInsets.all(16.0), child: const Text('未选中任何部门'));
        }

        return Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            border: const Border(top: BorderSide(color: Colors.grey)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '已选中部门 (${checkedDepartments.length}):',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 4,
                children:
                    checkedDepartments
                        .map(
                          (dept) => Chip(
                            label: Text(dept.departmentName),
                            onDeleted: () => _removeDepartmentSelection(dept),
                          ),
                        )
                        .toList(),
              ),
            ],
          ),
        );
      },
    );
  }

  /// 执行搜索
  void _performSearch(String query) {
    _treeKey.currentState?.searchDepartments(query.trim().isEmpty ? null : query);
  }

  /// 刷新数据
  void _refreshData() {
    _treeKey.currentState?.refresh();
  }

  /// 清除所有选择
  void _clearAllSelections() {
    final provider = Provider.of<DepartmentSelectorProvider>(context, listen: false);
    provider.clearAllChecked();
  }

  /// 移除单个部门选择
  void _removeDepartmentSelection(DepartmentModel department) {
    if (department.id != null) {
      final provider = Provider.of<DepartmentSelectorProvider>(context, listen: false);
      provider.setDepartmentChecked(department.id!, false);
    }
  }

  /// 显示选中的部门
  void _showSelectedDepartments() {
    final checkedDepartments = _treeKey.currentState?.getAllCheckedDepartments() ?? [];

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('选中的部门'),
            content: SizedBox(
              width: double.maxFinite,
              child:
                  checkedDepartments.isEmpty
                      ? const Text('未选中任何部门')
                      : ListView.builder(
                        shrinkWrap: true,
                        itemCount: checkedDepartments.length,
                        itemBuilder: (context, index) {
                          final dept = checkedDepartments[index];
                          return ListTile(
                            title: Text(dept.departmentName),
                            subtitle: Text('ID: ${dept.id}'),
                          );
                        },
                      ),
            ),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('关闭')),
            ],
          ),
    );
  }

  /// 节点点击回调
  void _onNodeTap(DepartmentModel department) {
    print('点击部门: ${department.departmentName}');
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('选中部门: ${department.departmentName}'),
        duration: const Duration(seconds: 1),
      ),
    );
  }

  /// 节点选择回调（复选框）
  void _onNodeSelected(DepartmentModel department, bool isSelected) {
    print('部门 ${department.departmentName} ${isSelected ? '被选中' : '取消选中'}');
  }
}

/// 简单的部门选择器示例（不使用 Provider）
class SimpleDepartmentSelectorExample extends StatefulWidget {
  const SimpleDepartmentSelectorExample({super.key});

  @override
  State<SimpleDepartmentSelectorExample> createState() => _SimpleDepartmentSelectorExampleState();
}

class _SimpleDepartmentSelectorExampleState extends State<SimpleDepartmentSelectorExample> {
  final TextEditingController _searchController = TextEditingController();
  final GlobalKey<DepartmentTreeState> _treeKey = GlobalKey<DepartmentTreeState>();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('简单部门选择器')),
      body: Column(
        children: [
          // 搜索栏
          Container(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: '搜索部门名称...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    _treeKey.currentState?.searchDepartments(null);
                  },
                ),
                border: const OutlineInputBorder(),
              ),
              onChanged: (query) {
                _treeKey.currentState?.searchDepartments(query.trim().isEmpty ? null : query);
              },
            ),
          ),

          // 部门树（使用 Provider）
          Expanded(
            child: ChangeNotifierProvider(
              create: (_) => DepartmentSelectorProvider()..loadDepartments(),
              child: DepartmentTree(
                key: _treeKey,
                showCheckbox: true,
                onNodeTap: (department) {
                  print('点击部门: ${department.departmentName}');
                },
                onNodeSelected: (department, isSelected) {
                  print('部门 ${department.departmentName} ${isSelected ? '被选中' : '取消选中'}');
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
